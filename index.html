<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Voice Journal</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    window.SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    console.log('HTML loaded, testing basic functionality');

    // Test basic JavaScript
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM loaded');
      const root = document.getElementById('root');
      if (root) {
        root.innerHTML = '<h1 style="color: red; padding: 20px;">Basic HTML Test - JavaScript is working!</h1><p>If you see this, the issue is with React/Vite, not basic loading.</p>';
        console.log('Basic content added to root');
      } else {
        console.error('Root element not found!');
      }
    });
  </script>
</head>
<body>
  <div id="root">
    <h1 style="color: blue; padding: 20px;">Fallback Content - If you see this, JavaScript hasn't run yet</h1>
  </div>
  <script type="module" src="/src/main.jsx"></script>
</body>
</html>