# Google OAuth Configuration
# Get your Google Client ID from: https://console.developers.google.com/
# 1. Create a new project or select existing project
# 2. Enable Google+ API
# 3. Create OAuth 2.0 Client ID credentials
# 4. Add your domain to authorized origins (e.g., http://localhost:5000)
VITE_GOOGLE_CLIENT_ID=your-google-client-id-here

# Backend API Configuration
VITE_API_BASE_URL=http://localhost:3000

# Development Configuration
VITE_DEV_MODE=true
