import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter, MemoryRouter } from 'react-router-dom';
import App from '../App';
import { UIProvider } from '../contexts/UIContext';

// Mock the server responses
const mockSuccessfulLogin = {
  accessToken: 'mock-access-token',
  refreshToken: 'mock-refresh-token',
  user: {
    userId: '123',
    username: 'testuser',
    name: 'Test User',
    email: '<EMAIL>'
  },
  expiresIn: '15m'
};

const mockSuccessfulRegister = {
  message: 'Registration successful',
  user: {
    userId: '124',
    username: 'newuser',
    email: '<EMAIL>'
  }
};

// Test wrapper
const TestWrapper = ({ children, initialEntries = ['/'] }) => (
  <MemoryRouter initialEntries={initialEntries}>
    <UIProvider>
      {children}
    </UIProvider>
  </MemoryRouter>
);

describe('Authentication Integration Tests', () => {
  beforeEach(() => {
    // Mock fetch
    global.fetch = vi.fn();
    
    // Mock localStorage and sessionStorage
    const mockStorage = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    };
    
    Object.defineProperty(window, 'localStorage', { value: mockStorage, writable: true });
    Object.defineProperty(window, 'sessionStorage', { value: mockStorage, writable: true });
    
    // Mock navigation
    Object.defineProperty(window, 'location', {
      value: { pathname: '/' },
      writable: true,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Login Flow', () => {
    it('should complete successful login flow with animations', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockSuccessfulLogin
      });

      render(
        <TestWrapper initialEntries={['/login']}>
          <App />
        </TestWrapper>
      );

      // Wait for login form to load
      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // Fill form
      fireEvent.change(usernameInput, { target: { value: 'testuser' } });
      fireEvent.change(passwordInput, { target: { value: 'TestPass123!' } });

      // Submit form
      fireEvent.click(submitButton);

      // Verify API call
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            username: 'testuser',
            password: 'TestPass123!',
            rememberMe: false
          })
        });
      });

      // Verify token storage
      expect(window.sessionStorage.setItem).toHaveBeenCalledWith('accessToken', 'mock-access-token');
      expect(window.sessionStorage.setItem).toHaveBeenCalledWith('refreshToken', 'mock-refresh-token');
    });

    it('should handle login errors gracefully', async () => {
      global.fetch.mockRejectedValueOnce({
        response: {
          ok: false,
          status: 401,
          json: async () => ({ error: 'Invalid credentials' })
        }
      });

      render(
        <TestWrapper initialEntries={['/login']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(usernameInput, { target: { value: 'testuser' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(submitButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
      });
    });

    it('should handle rate limiting', async () => {
      global.fetch.mockRejectedValueOnce({
        response: {
          ok: false,
          status: 429,
          json: async () => ({ error: 'Too many requests' })
        }
      });

      render(
        <TestWrapper initialEntries={['/login']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/too many requests/i)).toBeInTheDocument();
      });
    });
  });

  describe('Complete Registration Flow', () => {
    it('should complete successful registration flow', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockSuccessfulRegister
      });

      render(
        <TestWrapper initialEntries={['/register']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      // Fill form
      fireEvent.change(usernameInput, { target: { value: 'newuser' } });
      fireEvent.change(passwordInput, { target: { value: 'TestPass123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'TestPass123!' } });

      // Submit form
      fireEvent.click(submitButton);

      // Verify API call
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/register', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            username: 'newuser',
            password: 'TestPass123!'
          })
        });
      });
    });

    it('should handle registration validation errors', async () => {
      global.fetch.mockRejectedValueOnce({
        response: {
          ok: false,
          status: 400,
          json: async () => ({ 
            error: 'Validation failed',
            details: [{ msg: 'Username already exists' }]
          })
        }
      });

      render(
        <TestWrapper initialEntries={['/register']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      fireEvent.change(usernameInput, { target: { value: 'existinguser' } });
      fireEvent.change(passwordInput, { target: { value: 'TestPass123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'TestPass123!' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/username already exists/i)).toBeInTheDocument();
      });
    });
  });

  describe('Google OAuth Integration', () => {
    it('should handle Google OAuth login', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          ...mockSuccessfulLogin,
          user: {
            ...mockSuccessfulLogin.user,
            name: 'Google User',
            picture: 'https://example.com/avatar.jpg',
            isGoogleUser: true
          }
        })
      });

      render(
        <TestWrapper initialEntries={['/login']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('google-oauth-button')).toBeInTheDocument();
      });

      const googleButton = screen.getByTestId('google-oauth-button');
      fireEvent.click(googleButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/google', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            credential: 'mock-credential',
            rememberMe: false
          })
        });
      });
    });
  });

  describe('Token Management', () => {
    it('should handle token refresh', async () => {
      // Mock initial login
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockSuccessfulLogin
        })
        // Mock token refresh
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            accessToken: 'new-access-token',
            expiresIn: '15m'
          })
        });

      // Mock expired token scenario
      window.sessionStorage.getItem.mockImplementation((key) => {
        if (key === 'accessToken') return 'expired-token';
        if (key === 'refreshToken') return 'valid-refresh-token';
        if (key === 'tokenExpiry') return new Date(Date.now() - 1000).toISOString(); // Expired
        return null;
      });

      render(
        <TestWrapper initialEntries={['/dashboard']}>
          <App />
        </TestWrapper>
      );

      // Should attempt token refresh
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/refresh', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refreshToken: 'valid-refresh-token' })
        });
      });
    });

    it('should logout when refresh fails', async () => {
      global.fetch.mockRejectedValueOnce(new Error('Refresh failed'));

      window.sessionStorage.getItem.mockImplementation((key) => {
        if (key === 'accessToken') return 'expired-token';
        if (key === 'refreshToken') return 'invalid-refresh-token';
        if (key === 'tokenExpiry') return new Date(Date.now() - 1000).toISOString();
        return null;
      });

      render(
        <TestWrapper initialEntries={['/dashboard']}>
          <App />
        </TestWrapper>
      );

      // Should clear tokens and redirect to login
      await waitFor(() => {
        expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('accessToken');
        expect(window.sessionStorage.removeItem).toHaveBeenCalledWith('refreshToken');
      });
    });
  });

  describe('Performance Tests', () => {
    it('should load authentication components within performance budget', async () => {
      const startTime = performance.now();

      render(
        <TestWrapper initialEntries={['/login']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const loadTime = performance.now() - startTime;

      // Should load within 500ms
      expect(loadTime).toBeLessThan(500);
    });

    it('should handle rapid form interactions without performance degradation', async () => {
      render(
        <TestWrapper initialEntries={['/login']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const usernameInput = screen.getByLabelText(/username/i);
      const startTime = performance.now();

      // Simulate rapid typing
      for (let i = 0; i < 50; i++) {
        fireEvent.change(usernameInput, { target: { value: `test${i}` } });
      }

      const endTime = performance.now();
      const interactionTime = endTime - startTime;

      // Should handle rapid interactions within 100ms
      expect(interactionTime).toBeLessThan(100);
    });

    it('should maintain smooth animations during form submission', async () => {
      global.fetch.mockImplementation(() =>
        new Promise(resolve =>
          setTimeout(() => resolve({
            ok: true,
            json: async () => mockSuccessfulLogin
          }), 1000) // Simulate slow network
        )
      );

      render(
        <TestWrapper initialEntries={['/login']}>
          <App />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      });

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(usernameInput, { target: { value: 'testuser' } });
      fireEvent.change(passwordInput, { target: { value: 'TestPass123!' } });

      const animationStartTime = performance.now();
      fireEvent.click(submitButton);

      // Should show loading state immediately
      await waitFor(() => {
        expect(screen.getByText(/signing in/i)).toBeInTheDocument();
      });

      const animationTime = performance.now() - animationStartTime;

      // Animation should start within 50ms
      expect(animationTime).toBeLessThan(50);
    });
  });
});
