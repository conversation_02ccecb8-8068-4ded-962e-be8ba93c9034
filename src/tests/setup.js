import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock performance API
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    memory: {
      usedJSHeapSize: 1000000,
      totalJSHeapSize: 2000000,
      jsHeapSizeLimit: 4000000,
    },
  },
});

// Mock navigator
Object.defineProperty(window, 'navigator', {
  writable: true,
  value: {
    ...window.navigator,
    hardwareConcurrency: 4,
    deviceMemory: 8,
    connection: {
      effectiveType: '4g',
      downlink: 10,
      rtt: 100,
    },
  },
});

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id));

// Mock Google Identity Services
global.google = {
  accounts: {
    id: {
      initialize: vi.fn(),
      renderButton: vi.fn(),
      prompt: vi.fn(),
      disableAutoSelect: vi.fn(),
    },
  },
};

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args) => {
  // Suppress specific React warnings in tests
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
      args[0].includes('Warning: An invalid form control') ||
      args[0].includes('Warning: Failed prop type'))
  ) {
    return;
  }
  originalConsoleError.apply(console, args);
};

console.warn = (...args) => {
  // Suppress specific warnings in tests
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('componentWillReceiveProps') ||
      args[0].includes('componentWillUpdate'))
  ) {
    return;
  }
  originalConsoleWarn.apply(console, args);
};

// Mock environment variables
process.env.NODE_ENV = 'test';

// Global test utilities
global.testUtils = {
  // Helper to wait for animations to complete
  waitForAnimations: () => new Promise(resolve => setTimeout(resolve, 100)),

  // Helper to simulate user typing
  simulateTyping: (element, text, delay = 10) => {
    return new Promise(resolve => {
      let i = 0;
      const type = () => {
        if (i < text.length) {
          element.value += text[i];
          element.dispatchEvent(new Event('input', { bubbles: true }));
          i++;
          setTimeout(type, delay);
        } else {
          resolve();
        }
      };
      type();
    });
  },

  // Helper to mock API responses
  mockApiResponse: (data, status = 200, delay = 0) => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          ok: status >= 200 && status < 300,
          status,
          json: async () => data,
          text: async () => JSON.stringify(data),
        });
      }, delay);
    });
  },

  // Helper to create mock user data
  createMockUser: (overrides = {}) => ({
    userId: '123',
    username: 'testuser',
    email: '<EMAIL>',
    name: 'Test User',
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  // Helper to create mock credentials
  createMockCredentials: (overrides = {}) => ({
    username: 'testuser',
    password: 'TestPass123!',
    rememberMe: false,
    ...overrides,
  }),
};
