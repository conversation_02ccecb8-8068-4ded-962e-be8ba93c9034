import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { UIProvider } from '../contexts/UIContext';
import { EnhancedLoginForm, EnhancedSignupForm } from '../components/EnhancedAuthForms';

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
    form: ({ children, ...props }) => <form {...props}>{children}</form>,
    button: ({ children, ...props }) => <button {...props}>{children}</button>,
    p: ({ children, ...props }) => <p {...props}>{children}</p>,
    h2: ({ children, ...props }) => <h2 {...props}>{children}</h2>,
    svg: ({ children, ...props }) => <svg {...props}>{children}</svg>,
    path: ({ children, ...props }) => <path {...props}>{children}</path>,
  },
  AnimatePresence: ({ children }) => children,
  useAnimation: () => ({
    start: vi.fn(),
    set: vi.fn(),
  }),
}));

// Mock Google OAuth
vi.mock('../components/GoogleOAuth', () => ({
  GoogleOAuthButton: ({ onSuccess, onError, text }) => (
    <button 
      data-testid="google-oauth-button"
      onClick={() => onSuccess({ 
        credential: 'mock-credential',
        email: '<EMAIL>',
        name: 'Test User'
      })}
    >
      {text}
    </button>
  ),
}));

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <UIProvider>
      {children}
    </UIProvider>
  </BrowserRouter>
);

describe('Authentication Components', () => {
  beforeEach(() => {
    // Mock fetch for API calls
    global.fetch = vi.fn();
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    });

    // Mock sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('EnhancedLoginForm', () => {
    it('renders login form with all required fields', () => {
      render(
        <TestWrapper>
          <EnhancedLoginForm onLogin={vi.fn()} />
        </TestWrapper>
      );

      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
      expect(screen.getByTestId('google-oauth-button')).toBeInTheDocument();
    });

    it('validates required fields', async () => {
      render(
        <TestWrapper>
          <EnhancedLoginForm onLogin={vi.fn()} />
        </TestWrapper>
      );

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      
      // Try to submit without filling fields
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });
    });

    it('calls onLogin with correct credentials', async () => {
      const mockOnLogin = vi.fn();
      
      render(
        <TestWrapper>
          <EnhancedLoginForm onLogin={mockOnLogin} />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(usernameInput, { target: { value: 'testuser' } });
      fireEvent.change(passwordInput, { target: { value: 'TestPass123!' } });
      
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnLogin).toHaveBeenCalledWith({
          username: 'testuser',
          password: 'TestPass123!',
          rememberMe: false
        });
      });
    });

    it('handles Google OAuth login', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          accessToken: 'mock-access-token',
          refreshToken: 'mock-refresh-token',
          user: { name: 'Test User', email: '<EMAIL>' },
          expiresIn: '15m'
        })
      });

      render(
        <TestWrapper>
          <EnhancedLoginForm onLogin={vi.fn()} />
        </TestWrapper>
      );

      const googleButton = screen.getByTestId('google-oauth-button');
      fireEvent.click(googleButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/google', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            credential: 'mock-credential',
            rememberMe: false
          })
        });
      });
    });

    it('displays error messages', async () => {
      global.fetch.mockRejectedValueOnce(new Error('Login failed'));

      render(
        <TestWrapper>
          <EnhancedLoginForm onLogin={vi.fn()} />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(usernameInput, { target: { value: 'testuser' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/login failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('EnhancedSignupForm', () => {
    it('renders signup form with all required fields', () => {
      render(
        <TestWrapper>
          <EnhancedSignupForm onRegister={vi.fn()} />
        </TestWrapper>
      );

      expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
    });

    it('validates password strength', async () => {
      render(
        <TestWrapper>
          <EnhancedSignupForm onRegister={vi.fn()} />
        </TestWrapper>
      );

      const passwordInput = screen.getByLabelText(/^password$/i);
      
      // Test weak password
      fireEvent.change(passwordInput, { target: { value: 'weak' } });
      
      await waitFor(() => {
        expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
      });
    });

    it('validates password confirmation', async () => {
      render(
        <TestWrapper>
          <EnhancedSignupForm onRegister={vi.fn()} />
        </TestWrapper>
      );

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      
      fireEvent.change(passwordInput, { target: { value: 'TestPass123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'DifferentPass123!' } });
      
      await waitFor(() => {
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
      });
    });

    it('calls onRegister with correct data', async () => {
      const mockOnRegister = vi.fn();
      
      render(
        <TestWrapper>
          <EnhancedSignupForm onRegister={mockOnRegister} />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText(/username/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create account/i });

      fireEvent.change(usernameInput, { target: { value: 'newuser' } });
      fireEvent.change(passwordInput, { target: { value: 'TestPass123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'TestPass123!' } });
      
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnRegister).toHaveBeenCalledWith({
          username: 'newuser',
          password: 'TestPass123!'
        });
      });
    });
  });

  describe('Animation Performance', () => {
    it('should not cause performance issues during form interactions', async () => {
      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <EnhancedLoginForm onLogin={vi.fn()} />
        </TestWrapper>
      );

      const usernameInput = screen.getByLabelText(/username/i);
      
      // Simulate rapid typing
      for (let i = 0; i < 10; i++) {
        fireEvent.change(usernameInput, { target: { value: `test${i}` } });
      }

      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should complete within reasonable time (100ms)
      expect(renderTime).toBeLessThan(100);
    });
  });
});
