import React from 'react';
import <PERSON>act<PERSON><PERSON> from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import App from './App.jsx';

console.log('main.jsx loaded');
console.log('Environment:', import.meta.env);

// Test if root element exists
const rootElement = document.getElementById('root');
console.log('Root element found:', !!rootElement);

if (rootElement) {
  try {
    ReactDOM.createRoot(rootElement).render(
      <React.StrictMode>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </React.StrictMode>,
    );
    console.log('React app rendered successfully');
  } catch (error) {
    console.error('Error rendering React app:', error);
    // Fallback: show a simple message
    rootElement.innerHTML = '<h1>Error loading app. Check console for details.</h1>';
  }
} else {
  console.error('Root element not found!');
}
