// Token management utilities for the Recording Journal Web App

/**
 * Get stored access token from localStorage or sessionStorage
 * @returns {string|null} The stored access token or null if not found
 */
export const getStoredToken = () => {
  // Check for new token format first, then fallback to legacy
  return (
    localStorage.getItem('accessToken') ||
    sessionStorage.getItem('accessToken') ||
    localStorage.getItem('token') ||
    sessionStorage.getItem('token') ||
    null
  );
};

/**
 * Get stored refresh token from localStorage or sessionStorage
 * @returns {string|null} The stored refresh token or null if not found
 */
export const getStoredRefreshToken = () => {
  return (
    localStorage.getItem('refreshToken') ||
    sessionStorage.getItem('refreshToken') ||
    null
  );
};

/**
 * Get stored user data from localStorage or sessionStorage
 * @returns {Object|null} The stored user object or null if not found/invalid
 */
export const getStoredUser = () => {
  const savedUser =
    localStorage.getItem('user') || sessionStorage.getItem('user');
  try {
    return savedUser ? JSON.parse(savedUser) : null;
  } catch (e) {
    console.error('Failed to parse user from storage', e);
    clearStoredUser();
    return null;
  }
};

/**
 * Check if the current token is expired or about to expire
 * @param {number} bufferMinutes - Minutes before expiry to consider token expired (default: 2)
 * @returns {boolean} True if token is expired or about to expire
 */
export const isTokenExpired = (bufferMinutes = 2) => {
  const tokenExpiry =
    localStorage.getItem('tokenExpiry') ||
    sessionStorage.getItem('tokenExpiry');
  if (!tokenExpiry) return true;

  const expiryTime = parseInt(tokenExpiry);
  const bufferTime = bufferMinutes * 60 * 1000; // Convert to milliseconds

  return Date.now() >= expiryTime - bufferTime;
};

/**
 * Store tokens and user data in the appropriate storage
 * @param {Object} tokenData - Object containing accessToken, refreshToken, user, expiresIn
 * @param {boolean} rememberMe - Whether to use localStorage (true) or sessionStorage (false)
 */
export const storeTokenData = (tokenData, rememberMe = false) => {
  const { accessToken, refreshToken, user, expiresIn } = tokenData;
  const storage = rememberMe ? localStorage : sessionStorage;

  storage.setItem('accessToken', accessToken);
  if (refreshToken) {
    storage.setItem('refreshToken', refreshToken);
  }
  if (user) {
    storage.setItem('user', JSON.stringify(user));
  }

  // Calculate and store expiry time
  const expiryTime =
    Date.now() + (expiresIn === '24h' ? 24 * 60 * 60 * 1000 : 15 * 60 * 1000);
  storage.setItem('tokenExpiry', expiryTime.toString());

  // Handle remember me preferences
  if (rememberMe) {
    localStorage.setItem('rememberMe', 'true');
  } else {
    localStorage.removeItem('rememberMe');
  }
};

/**
 * Clear all stored tokens and user data
 * @param {boolean} keepRememberMe - Whether to keep remember me preferences (default: true)
 */
export const clearAllTokens = (keepRememberMe = true) => {
  ['localStorage', 'sessionStorage'].forEach(storageType => {
    const storage = window[storageType];
    storage.removeItem('accessToken');
    storage.removeItem('refreshToken');
    storage.removeItem('user');
    storage.removeItem('tokenExpiry');
    // Legacy token cleanup
    storage.removeItem('token');
  });

  if (!keepRememberMe) {
    localStorage.removeItem('rememberMe');
    localStorage.removeItem('rememberedUsername');
  }
};

/**
 * Clear only user data from storage
 */
export const clearStoredUser = () => {
  localStorage.removeItem('user');
  sessionStorage.removeItem('user');
};

/**
 * Get remember me preferences
 * @returns {Object} Object containing rememberMe boolean and rememberedUsername string
 */
export const getRememberMeData = () => {
  return {
    rememberMe: localStorage.getItem('rememberMe') === 'true',
    rememberedUsername: localStorage.getItem('rememberedUsername') || '',
  };
};

/**
 * Store remember me preferences
 * @param {string} username - Username to remember
 */
export const setRememberMeData = username => {
  localStorage.setItem('rememberMe', 'true');
  localStorage.setItem('rememberedUsername', username);
};

/**
 * Clear remember me preferences
 */
export const clearRememberMeData = () => {
  localStorage.removeItem('rememberMe');
  localStorage.removeItem('rememberedUsername');
};

/**
 * Check if user has valid authentication (token exists and not expired)
 * @returns {boolean} True if user is authenticated
 */
export const isAuthenticated = () => {
  const token = getStoredToken();
  return token && !isTokenExpired();
};

/**
 * Get the appropriate storage type based on current token location
 * @returns {'localStorage'|'sessionStorage'|null} The storage type or null if no tokens found
 */
export const getTokenStorageType = () => {
  if (localStorage.getItem('accessToken') || localStorage.getItem('token')) {
    return 'localStorage';
  }
  if (
    sessionStorage.getItem('accessToken') ||
    sessionStorage.getItem('token')
  ) {
    return 'sessionStorage';
  }
  return null;
};

/**
 * Migrate legacy token format to new format
 * This helps with backward compatibility
 */
export const migrateLegacyTokens = () => {
  // Check for legacy tokens and migrate them
  const legacyToken =
    localStorage.getItem('token') || sessionStorage.getItem('token');
  const legacyUser =
    localStorage.getItem('user') || sessionStorage.getItem('user');

  if (legacyToken && !getStoredToken()) {
    const storage = localStorage.getItem('token')
      ? localStorage
      : sessionStorage;
    storage.setItem('accessToken', legacyToken);

    if (legacyUser) {
      storage.setItem('user', legacyUser);
    }

    // Set a default expiry (15 minutes from now)
    storage.setItem('tokenExpiry', (Date.now() + 15 * 60 * 1000).toString());

    console.log('Migrated legacy tokens to new format');
  }
};
