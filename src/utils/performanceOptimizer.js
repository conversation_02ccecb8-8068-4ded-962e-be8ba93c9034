// Performance optimization utilities for the Recording Journal Web App

// Debounce utility for expensive operations
export const debounce = (func, wait, immediate = false) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
};

// Throttle utility for scroll/resize events
export const throttle = (func, limit) => {
  let inThrottle;
  return function (...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Intersection Observer for lazy loading
export const createIntersectionObserver = (callback, options = {}) => {
  const defaultOptions = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options,
  };

  if (!window.IntersectionObserver) {
    // Fallback for older browsers
    callback(true);
    return { disconnect: () => {} };
  }

  return new IntersectionObserver(entries => {
    entries.forEach(entry => {
      callback(entry.isIntersecting, entry);
    });
  }, defaultOptions);
};

// Memory usage monitoring
export const monitorMemoryUsage = () => {
  if (!performance.memory) {
    console.warn('Memory monitoring not available in this browser');
    return null;
  }

  const memory = performance.memory;
  const memoryInfo = {
    used: Math.round(memory.usedJSHeapSize / 1048576), // MB
    total: Math.round(memory.totalJSHeapSize / 1048576), // MB
    limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
    usage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100), // %
  };

  if (memoryInfo.usage > 80) {
    console.warn(`High memory usage detected: ${memoryInfo.usage}%`);
  }

  return memoryInfo;
};

// Bundle size analyzer
export const analyzeBundleSize = () => {
  const scripts = Array.from(document.querySelectorAll('script[src]'));
  const styles = Array.from(
    document.querySelectorAll('link[rel="stylesheet"]')
  );

  const analysis = {
    scripts: scripts.length,
    styles: styles.length,
    totalResources: scripts.length + styles.length,
  };

  console.log('Bundle Analysis:', analysis);
  return analysis;
};

// Animation performance optimizer
export const optimizeAnimations = () => {
  // Check for reduced motion preference
  const prefersReducedMotion = window.matchMedia(
    '(prefers-reduced-motion: reduce)'
  ).matches;

  // Check device capabilities
  const deviceCapabilities = {
    cores: navigator.hardwareConcurrency || 2,
    memory: navigator.deviceMemory || 2,
    connection: navigator.connection?.effectiveType || '4g',
  };

  // Determine animation quality level
  let animationQuality = 'high';

  if (prefersReducedMotion) {
    animationQuality = 'minimal';
  } else if (deviceCapabilities.cores <= 2 || deviceCapabilities.memory <= 2) {
    animationQuality = 'low';
  } else if (
    deviceCapabilities.connection === 'slow-2g' ||
    deviceCapabilities.connection === '2g'
  ) {
    animationQuality = 'medium';
  }

  return {
    quality: animationQuality,
    prefersReducedMotion,
    deviceCapabilities,
    recommendations: getAnimationRecommendations(animationQuality),
  };
};

// Get animation recommendations based on device capabilities
const getAnimationRecommendations = quality => {
  const recommendations = {
    minimal: {
      duration: 0.1,
      easing: 'linear',
      effects: ['opacity'],
      stagger: false,
      springs: false,
    },
    low: {
      duration: 0.2,
      easing: 'ease-out',
      effects: ['opacity', 'transform'],
      stagger: false,
      springs: false,
    },
    medium: {
      duration: 0.3,
      easing: 'ease-out',
      effects: ['opacity', 'transform', 'scale'],
      stagger: true,
      springs: false,
    },
    high: {
      duration: 0.6,
      easing: 'spring',
      effects: ['opacity', 'transform', 'scale', 'filter'],
      stagger: true,
      springs: true,
    },
  };

  return recommendations[quality] || recommendations.medium;
};

// Image optimization utilities
export const optimizeImages = {
  // Create responsive image srcSet
  createSrcSet: (baseUrl, sizes = [320, 640, 1024, 1280]) => {
    return sizes.map(size => `${baseUrl}?w=${size} ${size}w`).join(', ');
  },

  // Lazy load images
  lazyLoadImage: (
    img,
    src,
    placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGM0Y0RjYiLz48L3N2Zz4='
  ) => {
    img.src = placeholder;

    const observer = createIntersectionObserver(isVisible => {
      if (isVisible) {
        img.src = src;
        img.classList.add('loaded');
        observer.disconnect();
      }
    });

    observer.observe(img);
  },
};

// Code splitting utilities
export const loadComponent = async importFunc => {
  try {
    const startTime = performance.now();
    const module = await importFunc();
    const loadTime = performance.now() - startTime;

    console.log(`Component loaded in ${loadTime.toFixed(2)}ms`);
    return module;
  } catch (error) {
    console.error('Failed to load component:', error);
    throw error;
  }
};

// Performance metrics collection
export const collectPerformanceMetrics = () => {
  if (!window.performance) return null;

  const navigation = performance.getEntriesByType('navigation')[0];
  const paint = performance.getEntriesByType('paint');

  const metrics = {
    // Core Web Vitals
    FCP:
      paint.find(entry => entry.name === 'first-contentful-paint')?.startTime ||
      0,
    LCP: 0, // Would need PerformanceObserver for real LCP
    CLS: 0, // Would need PerformanceObserver for real CLS

    // Navigation timing
    domContentLoaded:
      navigation?.domContentLoadedEventEnd -
        navigation?.domContentLoadedEventStart || 0,
    loadComplete: navigation?.loadEventEnd - navigation?.loadEventStart || 0,

    // Resource timing
    totalResources: performance.getEntriesByType('resource').length,

    // Memory (if available)
    memory: monitorMemoryUsage(),
  };

  return metrics;
};

// Performance budget checker
export const checkPerformanceBudget = (metrics, budget = {}) => {
  const defaultBudget = {
    FCP: 1800, // 1.8s
    LCP: 2500, // 2.5s
    CLS: 0.1,
    totalResources: 50,
    memoryUsage: 80, // %
  };

  const finalBudget = { ...defaultBudget, ...budget };
  const violations = [];

  Object.keys(finalBudget).forEach(key => {
    if (metrics[key] > finalBudget[key]) {
      violations.push({
        metric: key,
        actual: metrics[key],
        budget: finalBudget[key],
        severity: metrics[key] > finalBudget[key] * 1.5 ? 'high' : 'medium',
      });
    }
  });

  return {
    passed: violations.length === 0,
    violations,
    score: Math.max(0, 100 - violations.length * 10),
  };
};

export default {
  debounce,
  throttle,
  createIntersectionObserver,
  monitorMemoryUsage,
  analyzeBundleSize,
  optimizeAnimations,
  optimizeImages,
  loadComponent,
  collectPerformanceMetrics,
  checkPerformanceBudget,
};
