import React, { lazy, Suspense } from 'react';
import { motion } from 'framer-motion';
import { AnimatedSpinner } from './AnimatedComponents';

// Lazy load authentication components
const EnhancedLoginForm = lazy(() =>
  import('./EnhancedAuthForms').then(module => ({
    default: module.EnhancedLoginForm,
  }))
);

const EnhancedSignupForm = lazy(() =>
  import('./EnhancedAuthForms').then(module => ({
    default: module.EnhancedSignupForm,
  }))
);

// Enhanced loading component with animations
const AuthLoadingFallback = ({ message = 'Loading authentication...' }) => (
  <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900'>
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className='flex flex-col items-center space-y-6'
    >
      {/* Animated logo/icon */}
      <motion.div
        animate={{
          rotate: [0, 360],
          scale: [1, 1.1, 1],
        }}
        transition={{
          rotate: { duration: 2, repeat: Infinity, ease: 'linear' },
          scale: { duration: 1.5, repeat: Infinity, ease: 'easeInOut' },
        }}
        className='w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center'
      >
        <motion.svg
          className='w-8 h-8 text-white'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z'
          />
        </motion.svg>
      </motion.div>

      {/* Loading spinner */}
      <div className='relative'>
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className='w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full'
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
          className='absolute inset-0 w-8 h-8 border-2 border-pink-500 border-b-transparent rounded-full'
        />
      </div>

      {/* Loading text */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className='text-center'
      >
        <p className='text-white text-lg font-medium'>{message}</p>
        <motion.div
          className='flex space-x-1 mt-2 justify-center'
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
        >
          {[0, 1, 2].map(i => (
            <motion.div
              key={i}
              className='w-2 h-2 bg-purple-400 rounded-full'
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </motion.div>
      </motion.div>
    </motion.div>
  </div>
);

// Lazy wrapper components with error boundaries
export const LazyLoginForm = ({ onLogin, ...props }) => (
  <Suspense
    fallback={<AuthLoadingFallback message='Loading sign in form...' />}
  >
    <EnhancedLoginForm onLogin={onLogin} {...props} />
  </Suspense>
);

export const LazySignupForm = ({ onRegister, ...props }) => (
  <Suspense
    fallback={<AuthLoadingFallback message='Loading sign up form...' />}
  >
    <EnhancedSignupForm onRegister={onRegister} {...props} />
  </Suspense>
);

// Performance monitoring hook
export const usePerformanceMonitor = () => {
  React.useEffect(() => {
    // Monitor frame rate for animations
    let frameCount = 0;
    let lastTime = performance.now();

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));

        // Log performance warnings
        if (fps < 30) {
          console.warn(
            `Low FPS detected: ${fps}fps. Consider reducing animation complexity.`
          );
        }

        frameCount = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);
  }, []);
};

// Optimized animation presets
export const optimizedAnimations = {
  // Reduced motion for better performance
  reduced: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2 },
  },

  // Standard animations
  standard: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: 'easeOut' },
  },

  // Enhanced animations for capable devices
  enhanced: {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { opacity: 1, y: 0, scale: 1 },
    exit: { opacity: 0, y: -20, scale: 0.95 },
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94],
      scale: { type: 'spring', stiffness: 300 },
    },
  },
};

// Device capability detection
export const getDeviceCapabilities = () => {
  const hasReducedMotion = window.matchMedia(
    '(prefers-reduced-motion: reduce)'
  ).matches;
  const isLowEndDevice = navigator.hardwareConcurrency <= 2;
  const hasSlowConnection =
    navigator.connection?.effectiveType === 'slow-2g' ||
    navigator.connection?.effectiveType === '2g';

  if (hasReducedMotion || isLowEndDevice || hasSlowConnection) {
    return 'reduced';
  } else if (navigator.hardwareConcurrency >= 8) {
    return 'enhanced';
  }

  return 'standard';
};

export default {
  LazyLoginForm,
  LazySignupForm,
  AuthLoadingFallback,
  usePerformanceMonitor,
  optimizedAnimations,
  getDeviceCapabilities,
};
