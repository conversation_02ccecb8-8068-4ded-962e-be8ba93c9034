import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  ValidatedInput,
  PasswordInput,
  PasswordConfirmInput,
} from './EnhancedFormComponents';
import {
  Animated<PERSON>utton,
  AnimatedSpinner,
  ScaleIn,
  AnimatedPage,
} from './AnimatedComponents';
import {
  useFormValidation,
  usePasswordValidation,
  useFormSubmission,
} from '../hooks/useFormValidation';
import { validateUsername, validatePassword } from '../utils/validation';
import { useUI } from '../contexts/UIContext';
import { GoogleOAuthButton } from './GoogleOAuth';

// Enhanced animation variants
const formVariants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94],
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    scale: 0.95,
    transition: {
      duration: 0.3,
      ease: 'easeInOut',
    },
  },
};

const fieldVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.4,
      ease: 'easeOut',
    },
  },
};

const successVariants = {
  hidden: { scale: 0, opacity: 0 },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 20,
    },
  },
};

const errorShakeVariants = {
  shake: {
    x: [-10, 10, -10, 10, 0],
    transition: {
      duration: 0.5,
      ease: 'easeInOut',
    },
  },
};

// Loading overlay component
const LoadingOverlay = ({ isVisible, message = 'Processing...' }) => (
  <AnimatePresence>
    {isVisible && (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className='absolute inset-0 bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-xl'
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          className='flex flex-col items-center space-y-4'
        >
          <div className='relative'>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              className='w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full'
            />
            <motion.div
              animate={{ rotate: -360 }}
              transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
              className='absolute inset-0 w-8 h-8 border-2 border-purple-500 border-b-transparent rounded-full'
            />
          </div>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className='text-white text-sm font-medium'
          >
            {message}
          </motion.p>
        </motion.div>
      </motion.div>
    )}
  </AnimatePresence>
);

// Success feedback component
const SuccessFeedback = ({ isVisible, message = 'Success!' }) => (
  <AnimatePresence>
    {isVisible && (
      <motion.div
        variants={successVariants}
        initial='hidden'
        animate='visible'
        exit='hidden'
        className='absolute inset-0 bg-green-500/90 backdrop-blur-sm flex items-center justify-center z-50 rounded-xl'
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 300 }}
          className='flex flex-col items-center space-y-3'
        >
          <div className='w-16 h-16 bg-white rounded-full flex items-center justify-center'>
            <motion.svg
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className='w-8 h-8 text-green-500'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={3}
                d='M5 13l4 4L19 7'
              />
            </motion.svg>
          </div>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className='text-white text-lg font-semibold'
          >
            {message}
          </motion.p>
        </motion.div>
      </motion.div>
    )}
  </AnimatePresence>
);

// Enhanced Login Form
export const EnhancedLoginForm = ({ onLogin }) => {
  const [rememberMe, setRememberMe] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const location = useLocation();
  const navigate = useNavigate();
  const { showSuccess: showUISuccess, showError } = useUI();
  const message = location.state?.message;
  const controls = useAnimation();

  const { isLoading, error, submitForm } = useFormSubmission();

  // Check for remembered credentials on component mount
  useEffect(() => {
    const wasRemembered = localStorage.getItem('rememberMe') === 'true';
    const rememberedUsername = localStorage.getItem('rememberedUsername');

    if (wasRemembered && rememberedUsername) {
      setRememberMe(true);
      // The username will be set in the form validation hook initialization
    }
  }, []);

  // Trigger shake animation on error
  useEffect(() => {
    if (error) {
      controls.start('shake');
    }
  }, [error, controls]);

  // Get remembered username for initial values
  const getInitialValues = () => {
    const wasRemembered = localStorage.getItem('rememberMe') === 'true';
    const rememberedUsername = localStorage.getItem('rememberedUsername');

    return {
      username: wasRemembered && rememberedUsername ? rememberedUsername : '',
      password: '',
    };
  };

  const {
    values,
    errors,
    touched,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    getFieldProps,
  } = useFormValidation(getInitialValues(), {
    username: {
      required: true,
      validator: validateUsername,
      requiredMessage: 'Username is required',
    },
    password: {
      required: true,
      validator: validatePassword,
      requiredMessage: 'Password is required',
    },
  });

  const onSubmit = async formData => {
    await submitForm(async () => {
      const loginData = { ...formData, rememberMe };
      await onLogin(loginData);

      // Handle remember me functionality
      if (rememberMe) {
        localStorage.setItem('rememberMe', 'true');
        localStorage.setItem('rememberedUsername', formData.username);
      } else {
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('rememberedUsername');
      }

      // Show success animation
      setSuccessMessage('Login successful!');
      setShowSuccess(true);

      // Hide success message after animation
      setTimeout(() => {
        setShowSuccess(false);
      }, 2000);

      showUISuccess('Welcome back!');
    });
  };

  const handleGoogleSuccess = async googleUserData => {
    await submitForm(async () => {
      try {
        // Send Google credential to backend
        const response = await fetch('/api/auth/google', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            credential: googleUserData.credential,
            rememberMe: rememberMe,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Google authentication failed');
        }

        const {
          accessToken,
          refreshToken,
          user: userData,
          expiresIn,
        } = await response.json();

        // Store tokens and user data based on rememberMe preference
        const storage = rememberMe ? localStorage : sessionStorage;
        storage.setItem('accessToken', accessToken);
        storage.setItem('refreshToken', refreshToken);
        storage.setItem('user', JSON.stringify(userData));
        storage.setItem(
          'tokenExpiry',
          new Date(
            Date.now() +
              (expiresIn === '24h' ? 24 * 60 * 60 * 1000 : 15 * 60 * 1000)
          ).toISOString()
        );

        // Show success animation
        setSuccessMessage(`Welcome ${userData.name}!`);
        setShowSuccess(true);

        // Hide success message after animation
        setTimeout(() => {
          setShowSuccess(false);
          navigate('/dashboard');
        }, 2000);

        showUISuccess(`Welcome back, ${userData.name}!`);
      } catch (error) {
        console.error('Google OAuth error:', error);
        throw error;
      }
    });
  };

  const handleGoogleError = error => {
    console.error('Google OAuth error:', error);
    showError('Google Sign-In failed. Please try again.');
  };

  return (
    <AnimatedPage className='min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 px-4'>
      <ScaleIn className='w-full max-w-md'>
        <motion.div
          className='bg-gray-800/50 backdrop-blur-xl rounded-2xl p-8 border border-white/10 shadow-2xl relative overflow-hidden'
          variants={formVariants}
          initial='hidden'
          animate='visible'
          exit='exit'
          animate={controls}
        >
          {/* Loading Overlay */}
          <LoadingOverlay isVisible={isLoading} message='Signing you in...' />

          {/* Success Overlay */}
          <SuccessFeedback isVisible={showSuccess} message={successMessage} />

          {/* Header */}
          <motion.div className='text-center mb-8' variants={fieldVariants}>
            <motion.h2
              className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500'
              whileHover={{ scale: 1.05 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              Welcome Back
            </motion.h2>
            <motion.p
              className='text-gray-400 mt-2'
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              Sign in to your account
            </motion.p>
          </motion.div>

          {/* Success message from registration */}
          <AnimatePresence>
            {message && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className='mb-6 p-4 bg-green-900/30 border border-green-500/30 rounded-lg text-green-400 text-center'
              >
                {message}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Error message */}
          <AnimatePresence>
            {error && (
              <motion.div
                variants={errorShakeVariants}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className='mb-6 p-4 bg-red-900/30 border border-red-500/30 rounded-lg text-red-400 text-center relative overflow-hidden'
              >
                <motion.div
                  className='absolute inset-0 bg-red-500/10'
                  initial={{ x: '-100%' }}
                  animate={{ x: '100%' }}
                  transition={{ duration: 0.6, ease: 'easeInOut' }}
                />
                <span className='relative z-10'>{error}</span>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Form */}
          <motion.form
            onSubmit={handleSubmit(onSubmit)}
            className='space-y-6'
            variants={formVariants}
          >
            <motion.div variants={fieldVariants}>
              <ValidatedInput
                type='text'
                placeholder='Enter your username'
                label='Username'
                required
                validator={validateUsername}
                {...getFieldProps('username')}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <ValidatedInput
                type='password'
                placeholder='Enter your password'
                label='Password'
                required
                validator={validatePassword}
                {...getFieldProps('password')}
              />
            </motion.div>

            {/* Remember Me */}
            <motion.div
              className='flex items-center justify-between'
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <label className='flex items-center space-x-2 cursor-pointer'>
                <motion.input
                  type='checkbox'
                  checked={rememberMe}
                  onChange={e => setRememberMe(e.target.checked)}
                  className='w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500 focus:ring-2'
                  whileTap={{ scale: 0.95 }}
                />
                <span className='text-sm text-gray-300'>Remember me</span>
              </label>

              <motion.button
                type='button'
                onClick={() => navigate('/forgot-password')}
                className='text-sm text-purple-400 hover:text-purple-300 transition-colors'
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Forgot password?
              </motion.button>
            </motion.div>

            {/* Submit Button */}
            <motion.div variants={fieldVariants}>
              <motion.button
                type='submit'
                disabled={!isValid || isLoading}
                className='w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-lg text-white font-semibold transition-all duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group'
                whileHover={{
                  scale: 1.02,
                  boxShadow: '0 10px 25px rgba(147, 51, 234, 0.3)',
                }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <motion.div
                  className='absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300'
                  initial={false}
                />
                <span className='relative z-10 flex items-center'>
                  {isLoading ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: 'linear',
                        }}
                        className='w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2'
                      />
                      Signing in...
                    </>
                  ) : (
                    <>
                      Sign In
                      <motion.svg
                        className='w-5 h-5 ml-2'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                        initial={{ x: 0 }}
                        whileHover={{ x: 5 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M13 7l5 5m0 0l-5 5m5-5H6'
                        />
                      </motion.svg>
                    </>
                  )}
                </span>
              </motion.button>
            </motion.div>
          </motion.form>

          {/* Divider */}
          <motion.div
            className='flex items-center my-6'
            variants={fieldVariants}
          >
            <motion.div
              className='flex-1 border-t border-gray-600'
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            />
            <motion.span
              className='px-4 text-gray-400 text-sm'
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8 }}
            >
              or
            </motion.span>
            <motion.div
              className='flex-1 border-t border-gray-600'
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            />
          </motion.div>

          {/* Google OAuth Button */}
          <motion.div variants={fieldVariants}>
            <motion.div
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              <GoogleOAuthButton
                onSuccess={handleGoogleSuccess}
                onError={handleGoogleError}
                disabled={isLoading}
                text='Sign in with Google'
              />
            </motion.div>
          </motion.div>

          {/* Register Link */}
          <motion.p
            className='text-center text-gray-400 text-sm mt-6'
            variants={fieldVariants}
          >
            Don't have an account?{' '}
            <motion.button
              onClick={() => navigate('/register')}
              className='text-purple-400 hover:text-purple-300 underline font-medium'
              whileHover={{
                scale: 1.05,
                color: '#c084fc',
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              Create one here
            </motion.button>
          </motion.p>
        </motion.div>
      </ScaleIn>
    </AnimatedPage>
  );
};

// Enhanced Register Form
export const EnhancedRegisterForm = ({ onRegister }) => {
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();
  const { showSuccess: showUISuccess, showError } = useUI();
  const { isLoading, error, submitForm } = useFormSubmission();
  const controls = useAnimation();

  // Trigger shake animation on error
  useEffect(() => {
    if (error) {
      controls.start('shake');
    }
  }, [error, controls]);

  const {
    password,
    confirmPassword,
    setPassword,
    setConfirmPassword,
    passwordValidation,
    confirmValidation,
    handlePasswordValidation,
    handleConfirmValidation,
    isPasswordValid,
  } = usePasswordValidation();

  const {
    values,
    errors,
    touched,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    getFieldProps,
  } = useFormValidation(
    { username: '' },
    {
      username: {
        required: true,
        validator: validateUsername,
        requiredMessage: 'Username is required',
      },
    }
  );

  const formIsValid =
    isValid &&
    isPasswordValid &&
    password.length > 0 &&
    confirmPassword.length > 0;

  const onSubmit = async formData => {
    await submitForm(async () => {
      await onRegister({ ...formData, password });

      // Show success animation
      setSuccessMessage('Account created successfully!');
      setShowSuccess(true);

      // Hide success message after animation
      setTimeout(() => {
        setShowSuccess(false);
      }, 2000);

      showUISuccess('Registration successful! Please log in.');
    });
  };

  const handleGoogleSuccess = async googleUserData => {
    await submitForm(async () => {
      try {
        // Send Google credential to backend for registration/login
        const response = await fetch('/api/auth/google', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            credential: googleUserData.credential,
            rememberMe: true, // Google signups are typically remembered
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Google authentication failed');
        }

        const {
          accessToken,
          refreshToken,
          user: userData,
          expiresIn,
        } = await response.json();

        // Store tokens and user data
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem(
          'tokenExpiry',
          new Date(
            Date.now() +
              (expiresIn === '24h' ? 24 * 60 * 60 * 1000 : 15 * 60 * 1000)
          ).toISOString()
        );

        // Show success animation
        setSuccessMessage(
          `Welcome ${userData.name}! Account created successfully.`
        );
        setShowSuccess(true);

        // Hide success message after animation
        setTimeout(() => {
          setShowSuccess(false);
          navigate('/dashboard');
        }, 2000);

        showUISuccess(
          `Welcome ${userData.name}! Account created successfully.`
        );
      } catch (error) {
        console.error('Google OAuth registration error:', error);
        throw error;
      }
    });
  };

  const handleGoogleError = error => {
    console.error('Google OAuth registration error:', error);
    showError('Google Sign-Up failed. Please try again.');
  };

  return (
    <AnimatedPage className='min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 px-4'>
      <ScaleIn className='w-full max-w-md'>
        <motion.div
          className='bg-gray-800/50 backdrop-blur-xl rounded-2xl p-8 border border-white/10 shadow-2xl relative overflow-hidden'
          variants={formVariants}
          initial='hidden'
          animate='visible'
          exit='exit'
          animate={controls}
        >
          {/* Loading Overlay */}
          <LoadingOverlay
            isVisible={isLoading}
            message='Creating your account...'
          />

          {/* Success Overlay */}
          <SuccessFeedback isVisible={showSuccess} message={successMessage} />

          {/* Header */}
          <motion.div className='text-center mb-8' variants={fieldVariants}>
            <motion.h2
              className='text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500'
              whileHover={{ scale: 1.05 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              Create Account
            </motion.h2>
            <motion.p
              className='text-gray-400 mt-2'
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              Join us and start your journey
            </motion.p>
          </motion.div>

          {/* Error message */}
          <AnimatePresence>
            {error && (
              <motion.div
                variants={errorShakeVariants}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className='mb-6 p-4 bg-red-900/30 border border-red-500/30 rounded-lg text-red-400 text-center relative overflow-hidden'
              >
                <motion.div
                  className='absolute inset-0 bg-red-500/10'
                  initial={{ x: '-100%' }}
                  animate={{ x: '100%' }}
                  transition={{ duration: 0.6, ease: 'easeInOut' }}
                />
                <span className='relative z-10'>{error}</span>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Form */}
          <motion.form
            onSubmit={handleSubmit(onSubmit)}
            className='space-y-6'
            variants={formVariants}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <motion.div variants={fieldVariants}>
              <ValidatedInput
                type='text'
                placeholder='Choose a username'
                label='Username'
                required
                validator={validateUsername}
                {...getFieldProps('username')}
              />
            </motion.div>

            <motion.div variants={fieldVariants}>
              <PasswordInput
                value={password}
                onChange={e => setPassword(e.target.value)}
                onValidation={handlePasswordValidation}
                placeholder='Create a strong password'
                label='Password'
                required
                showStrength
              />
            </motion.div>

            <motion.div variants={fieldVariants}>
              <PasswordConfirmInput
                value={confirmPassword}
                password={password}
                onChange={e => setConfirmPassword(e.target.value)}
                onValidation={handleConfirmValidation}
                placeholder='Confirm your password'
                label='Confirm Password'
                required
              />
            </motion.div>

            {/* Submit Button */}
            <motion.div variants={fieldVariants}>
              <motion.button
                type='submit'
                disabled={!formIsValid || isLoading}
                className='w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-lg text-white font-semibold transition-all duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group'
                whileHover={{
                  scale: 1.02,
                  boxShadow: '0 10px 25px rgba(147, 51, 234, 0.3)',
                }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <motion.div
                  className='absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300'
                  initial={false}
                />
                <span className='relative z-10 flex items-center'>
                  {isLoading ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          ease: 'linear',
                        }}
                        className='w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2'
                      />
                      Creating Account...
                    </>
                  ) : (
                    <>
                      Create Account
                      <motion.svg
                        className='w-5 h-5 ml-2'
                        fill='none'
                        stroke='currentColor'
                        viewBox='0 0 24 24'
                        initial={{ x: 0 }}
                        whileHover={{ x: 5 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                      >
                        <path
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          strokeWidth={2}
                          d='M12 6v6m0 0v6m0-6h6m-6 0H6'
                        />
                      </motion.svg>
                    </>
                  )}
                </span>
              </motion.button>
            </motion.div>
          </motion.form>

          {/* Divider */}
          <motion.div
            className='flex items-center my-6'
            variants={fieldVariants}
          >
            <motion.div
              className='flex-1 border-t border-gray-600'
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            />
            <motion.span
              className='px-4 text-gray-400 text-sm'
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8 }}
            >
              or
            </motion.span>
            <motion.div
              className='flex-1 border-t border-gray-600'
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            />
          </motion.div>

          {/* Google OAuth Button */}
          <motion.div variants={fieldVariants}>
            <motion.div
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              <GoogleOAuthButton
                onSuccess={handleGoogleSuccess}
                onError={handleGoogleError}
                disabled={isLoading}
                text='Sign up with Google'
              />
            </motion.div>
          </motion.div>

          {/* Login Link */}
          <motion.p
            className='text-center text-gray-400 text-sm mt-6'
            variants={fieldVariants}
          >
            Already have an account?{' '}
            <motion.button
              onClick={() => navigate('/login')}
              className='text-purple-400 hover:text-purple-300 underline font-medium'
              whileHover={{
                scale: 1.05,
                color: '#c084fc',
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 300 }}
            >
              Sign in here
            </motion.button>
          </motion.p>
        </motion.div>
      </ScaleIn>
    </AnimatedPage>
  );
};
