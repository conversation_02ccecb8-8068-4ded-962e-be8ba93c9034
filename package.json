{"scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server.js", "vercel-build": "vite build", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@types/google-one-tap": "^1.2.6", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "framer-motion": "^12.19.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "uuid": "^11.1.0", "vercel": "^44.2.5", "xss-clean": "^0.1.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@vitejs/plugin-react": "^4.6.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.21", "jsdom": "^23.0.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "terser": "^5.43.1", "vite": "^4.3.9", "vitest": "^1.0.4"}}