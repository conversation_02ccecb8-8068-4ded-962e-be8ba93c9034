// Backend: server.js
const express = require('express');
const fs = require('fs').promises; // Use promises version of fs
const path = require('path');
const bodyParser = require('body-parser');
const cors = require('cors');
const bcrypt = require('bcrypt'); // For password hashing
const jwt = require('jsonwebtoken'); // For JWT
const { v4: uuidv4 } = require('uuid'); // For user IDs
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const { body, validationResult, param } = require('express-validator');
const mongoSanitize = require('express-mongo-sanitize');

// Environment variables
const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';
const DATA_FILE = path.join(__dirname, 'entries.json');
const USERS_FILE = path.join(__dirname, 'users.json'); // Path for users file
const JWT_SECRET = process.env.JWT_SECRET || 'your-very-secure-secret'; // IMPORTANT: Use env var in production!
const JWT_REFRESH_SECRET =
  process.env.JWT_REFRESH_SECRET || 'your-very-secure-refresh-secret';
const REFRESH_TOKENS_FILE = path.join(__dirname, 'refresh_tokens.json');

// Initialize express app
const app = express();

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
        fontSrc: ["'self'", 'https://fonts.gstatic.com'],
        scriptSrc: ["'self'", 'https://accounts.google.com'],
        connectSrc: ["'self'", 'https://accounts.google.com'],
        imgSrc: ["'self'", 'data:', 'https:'],
      },
    },
    crossOriginEmbedderPolicy: false,
  })
);

// Rate limiting
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs for auth endpoints
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: Math.ceil((15 * 60) / 60), // minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
});

const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests, please try again later.',
    retryAfter: Math.ceil((15 * 60) / 60), // minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Configure CORS - this can be adjusted for production
const corsOptions = {
  origin:
    NODE_ENV === 'production'
      ? function (origin, callback) {
          // Allow requests with no origin (like mobile apps or curl requests)
          if (!origin) return callback(null, true);

          // Allow Vercel deployment URLs and custom domains
          const allowedOrigins = [
            /^https:\/\/.*\.vercel\.app$/,
            /^https:\/\/recording-journal.*\.vercel\.app$/,
            // Add your custom domain here when you have one
            // 'https://yourdomain.com'
          ];

          const isAllowed = allowedOrigins.some(pattern => {
            if (typeof pattern === 'string') {
              return pattern === origin;
            }
            return pattern.test(origin);
          });

          callback(null, isAllowed);
        }
      : true, // Allow all origins in development
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
};

app.use(cors(corsOptions));
app.use(generalLimiter); // Apply general rate limiting to all routes
app.use(bodyParser.json({ limit: '10mb' }));
app.use(mongoSanitize()); // Prevent NoSQL injection attacks

// Request logging middleware
app.use((req, res, next) => {
  console.log(
    `${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip}`
  );
  next();
});

// --- User Helper Functions ---

const readUsers = async () => {
  try {
    await fs.access(USERS_FILE); // Check if file exists
    const data = await fs.readFile(USERS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    if (error.code === 'ENOENT') {
      // File does not exist
      console.log('users.json not found, creating and returning empty object.');
      await writeUsers({}); // Create the file with an empty object
      return {};
    } else {
      console.error('Error reading users file:', error);
      throw new Error('Failed to read user data');
    }
  }
};

const writeUsers = async users => {
  try {
    await fs.writeFile(USERS_FILE, JSON.stringify(users, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('Error writing users file:', error);
    throw new Error('Failed to write user data');
  }
};

// --- Refresh Token Helper Functions ---

const readRefreshTokens = async () => {
  try {
    await fs.access(REFRESH_TOKENS_FILE);
    const data = await fs.readFile(REFRESH_TOKENS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.log(
        'refresh_tokens.json not found, creating and returning empty object.'
      );
      await writeRefreshTokens({});
      return {};
    } else {
      console.error('Error reading refresh tokens file:', error);
      throw new Error('Failed to read refresh token data');
    }
  }
};

const writeRefreshTokens = async tokens => {
  try {
    await fs.writeFile(
      REFRESH_TOKENS_FILE,
      JSON.stringify(tokens, null, 2),
      'utf8'
    );
    return true;
  } catch (error) {
    console.error('Error writing refresh tokens file:', error);
    throw new Error('Failed to write refresh token data');
  }
};

// Clean up expired refresh tokens
const cleanupExpiredTokens = async () => {
  try {
    const tokens = await readRefreshTokens();
    const now = Date.now();
    const validTokens = {};

    Object.keys(tokens).forEach(tokenId => {
      if (tokens[tokenId].expiresAt > now) {
        validTokens[tokenId] = tokens[tokenId];
      }
    });

    await writeRefreshTokens(validTokens);
    console.log('Expired refresh tokens cleaned up');
  } catch (error) {
    console.error('Error cleaning up expired tokens:', error);
  }
};

// --- Entry Helper Functions (Modified for User-Specific Data) ---

// Reads the entire entries structure
const readAllEntriesData = async () => {
  try {
    await fs.access(DATA_FILE); // Check if file exists
    const data = await fs.readFile(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    if (error.code === 'ENOENT') {
      // File does not exist
      console.log(
        'entries.json not found, creating and returning empty object.'
      );
      await writeAllEntriesData({}); // Create the file with an empty object
      return {};
    } else {
      console.error('Error reading entries file:', error);
      throw new Error('Failed to read entry data');
    }
  }
};

// Writes the entire entries structure
const writeAllEntriesData = async allEntries => {
  try {
    await fs.writeFile(DATA_FILE, JSON.stringify(allEntries, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('Error writing entries file:', error);
    throw new Error('Failed to write entry data');
  }
};

// --- Authentication Middleware ---

const verifyToken = (req, res, next) => {
  const bearerHeader = req.headers['authorization'];

  if (typeof bearerHeader !== 'undefined') {
    const bearer = bearerHeader.split(' ');
    const bearerToken = bearer[1];

    if (!bearerToken) {
      return res
        .status(401)
        .json({ error: 'Invalid token format', code: 'INVALID_TOKEN_FORMAT' });
    }

    jwt.verify(bearerToken, JWT_SECRET, (err, decoded) => {
      if (err) {
        console.error('JWT Verification Error:', err.message);
        // Differentiate between expired and invalid tokens
        if (err.name === 'TokenExpiredError') {
          return res
            .status(401)
            .json({ error: 'Token expired', code: 'TOKEN_EXPIRED' });
        } else {
          return res
            .status(403)
            .json({ error: 'Invalid token', code: 'INVALID_TOKEN' });
        }
      }
      // Add userId to request object
      req.userId = decoded.userId;
      req.userRole = decoded.role || 'user';
      console.log(`Authenticated user: ${req.userId}`);
      next();
    });
  } else {
    // Forbidden
    console.log('Authorization header missing');
    res.status(401).json({ error: 'Authorization required', code: 'NO_TOKEN' });
  }
};

// Input validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value,
    }));
    return res.status(400).json({
      error: 'Validation failed',
      details: errorMessages,
    });
  }
  next();
};

// Enhanced password validation
const validatePassword = password => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const errors = [];

  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  if (!hasUpperCase) {
    errors.push('Password must contain at least one uppercase letter');
  }
  if (!hasLowerCase) {
    errors.push('Password must contain at least one lowercase letter');
  }
  if (!hasNumbers) {
    errors.push('Password must contain at least one number');
  }
  if (!hasSpecialChar) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength: calculatePasswordStrength(password),
  };
};

const calculatePasswordStrength = password => {
  let score = 0;

  // Length bonus
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;

  // Character variety bonus
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;

  // Complexity bonus
  if (
    password.length >= 10 &&
    /(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^A-Za-z0-9])/.test(password)
  ) {
    score += 1;
  }

  if (score <= 2) return 'weak';
  if (score <= 4) return 'fair';
  if (score <= 6) return 'good';
  return 'strong';
};

// --- Auth API Routes ---

// Validation rules for registration
const registerValidation = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores')
    .escape(),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .custom(value => {
      const validation = validatePassword(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    }),
];

// Google OAuth authentication
app.post(
  '/api/auth/google',
  authLimiter,
  [
    body('credential').notEmpty().withMessage('Google credential is required'),
    mongoSanitize(),
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { credential, rememberMe = false } = req.body;

      // In a real implementation, you would verify the Google JWT token
      // For now, we'll decode it (without verification for demo purposes)
      const payload = JSON.parse(
        Buffer.from(credential.split('.')[1], 'base64').toString()
      );

      const { email, name, picture, sub: googleId } = payload;

      const users = await readUsers();

      // Check if user exists
      let user = users.find(u => u.email === email || u.googleId === googleId);

      if (!user) {
        // Create new user from Google data
        user = {
          userId: generateUserId(),
          username: email.split('@')[0], // Use email prefix as username
          email,
          name,
          picture,
          googleId,
          password: null, // No password for Google users
          createdAt: new Date().toISOString(),
          lastLogin: new Date().toISOString(),
          loginAttempts: 0,
          accountLocked: false,
          lockUntil: null,
          isGoogleUser: true,
        };

        users.push(user);
        await writeUsers(users);
      } else {
        // Update existing user's Google info
        user.name = name;
        user.picture = picture;
        user.googleId = googleId;
        user.lastLogin = new Date().toISOString();
        user.loginAttempts = 0;
        user.accountLocked = false;
        user.lockUntil = null;
        user.isGoogleUser = true;
        await writeUsers(users);
      }

      // Generate tokens
      const accessTokenExpiry = rememberMe ? '24h' : '15m';
      const refreshTokenExpiry = rememberMe ? '30d' : '7d';

      const accessToken = jwt.sign(
        { userId: user.userId, username: user.username },
        JWT_SECRET,
        { expiresIn: accessTokenExpiry }
      );

      const refreshToken = jwt.sign(
        { userId: user.userId, type: 'refresh' },
        JWT_REFRESH_SECRET,
        { expiresIn: refreshTokenExpiry }
      );

      // Store refresh token
      const refreshTokenData = {
        token: refreshToken,
        userId: user.userId,
        expiresAt: new Date(
          Date.now() +
            (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000)
        ),
        createdAt: new Date(),
      };

      const refreshTokens = await readRefreshTokens();
      refreshTokens.push(refreshTokenData);
      await writeRefreshTokens(refreshTokens);

      // Return user data without sensitive information
      const { password, loginAttempts, accountLocked, lockUntil, ...safeUser } =
        user;

      res.json({
        message: 'Google authentication successful',
        accessToken,
        refreshToken,
        user: safeUser,
        expiresIn: accessTokenExpiry,
      });
    } catch (error) {
      console.error('Google OAuth error:', error);
      res.status(500).json({ error: 'Google authentication failed' });
    }
  }
);

// Register new user
app.post(
  '/api/auth/register',
  authLimiter,
  registerValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { username, password, email } = req.body;

      const users = await readUsers();

      // Check if username already exists (case-insensitive check)
      const existingUser = Object.values(users).find(
        u =>
          u.username.toLowerCase() === username.toLowerCase() ||
          (email && u.email && u.email.toLowerCase() === email.toLowerCase())
      );

      if (existingUser) {
        return res.status(409).json({
          error: 'User already exists',
          field:
            existingUser.username.toLowerCase() === username.toLowerCase()
              ? 'username'
              : 'email',
        });
      }

      // Hash password with higher salt rounds for better security
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(password, salt);
      const userId = uuidv4();

      // Store new user with additional security fields
      const newUser = {
        userId,
        username: username.trim(),
        email: email ? email.trim().toLowerCase() : null,
        password: hashedPassword,
        createdAt: new Date().toISOString(),
        lastLogin: null,
        isActive: true,
        loginAttempts: 0,
        lockUntil: null,
      };

      users[userId] = newUser;
      await writeUsers(users);

      console.log(`User registered: ${username} (ID: ${userId})`);

      // Generate tokens for immediate login
      const accessToken = jwt.sign(
        { userId, username, role: 'user' },
        JWT_SECRET,
        { expiresIn: '15m' }
      );

      const refreshToken = jwt.sign(
        { userId, type: 'refresh' },
        JWT_REFRESH_SECRET,
        { expiresIn: '7d' }
      );

      // Store refresh token
      const refreshTokens = await readRefreshTokens();
      const refreshTokenId = uuidv4();
      refreshTokens[refreshTokenId] = {
        userId,
        token: refreshToken,
        createdAt: Date.now(),
        expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
        userAgent: req.headers['user-agent'] || 'unknown',
      };
      await writeRefreshTokens(refreshTokens);

      res.status(201).json({
        message: 'User registered successfully',
        accessToken,
        refreshToken,
        user: {
          userId,
          username,
          email: newUser.email,
        },
      });
    } catch (error) {
      console.error('Registration Error:', error);
      res.status(500).json({
        error: 'Registration failed',
        message:
          NODE_ENV === 'development' ? error.message : 'Internal server error',
      });
    }
  }
);

// Validation rules for login
const loginValidation = [
  body('username')
    .trim()
    .notEmpty()
    .withMessage('Username is required')
    .escape(),
  body('password').notEmpty().withMessage('Password is required'),
];

// Login user
app.post(
  '/api/auth/login',
  authLimiter,
  loginValidation,
  handleValidationErrors,
  async (req, res) => {
    try {
      const { username, password, rememberMe = false } = req.body;
      const users = await readUsers();

      // Find user by username or email (case-insensitive)
      const user = Object.values(users).find(
        u =>
          u.username.toLowerCase() === username.toLowerCase() ||
          (u.email && u.email.toLowerCase() === username.toLowerCase())
      );

      if (!user) {
        console.log(`Login failed: User not found - ${username}`);
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Check if account is locked
      if (user.lockUntil && user.lockUntil > Date.now()) {
        const lockTimeRemaining = Math.ceil(
          (user.lockUntil - Date.now()) / (1000 * 60)
        );
        return res.status(423).json({
          error: 'Account temporarily locked',
          message: `Account locked for ${lockTimeRemaining} minutes due to multiple failed login attempts`,
        });
      }

      // Check if account is active
      if (!user.isActive) {
        return res.status(403).json({ error: 'Account is deactivated' });
      }

      // Compare password
      const isMatch = await bcrypt.compare(password, user.password);

      if (!isMatch) {
        // Increment login attempts
        user.loginAttempts = (user.loginAttempts || 0) + 1;

        // Lock account after 5 failed attempts for 30 minutes
        if (user.loginAttempts >= 5) {
          user.lockUntil = Date.now() + 30 * 60 * 1000; // 30 minutes
          console.log(
            `Account locked for user: ${username} due to multiple failed attempts`
          );
        }

        users[user.userId] = user;
        await writeUsers(users);

        console.log(
          `Login failed: Incorrect password for user - ${username} (Attempts: ${user.loginAttempts})`
        );
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Reset login attempts on successful login
      user.loginAttempts = 0;
      user.lockUntil = null;
      user.lastLogin = new Date().toISOString();
      users[user.userId] = user;
      await writeUsers(users);

      // Generate tokens
      const accessTokenExpiry = rememberMe ? '24h' : '15m';
      const refreshTokenExpiry = rememberMe ? '30d' : '7d';

      const accessToken = jwt.sign(
        { userId: user.userId, username: user.username, role: 'user' },
        JWT_SECRET,
        { expiresIn: accessTokenExpiry }
      );

      const refreshToken = jwt.sign(
        { userId: user.userId, type: 'refresh' },
        JWT_REFRESH_SECRET,
        { expiresIn: refreshTokenExpiry }
      );

      // Store refresh token
      const refreshTokens = await readRefreshTokens();
      const refreshTokenId = uuidv4();
      const expiresAt =
        Date.now() +
        (rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000);

      refreshTokens[refreshTokenId] = {
        userId: user.userId,
        token: refreshToken,
        createdAt: Date.now(),
        expiresAt,
        userAgent: req.headers['user-agent'] || 'unknown',
        rememberMe,
      };
      await writeRefreshTokens(refreshTokens);

      console.log(`User logged in: ${username} (ID: ${user.userId})`);

      res.json({
        accessToken,
        refreshToken,
        user: {
          userId: user.userId,
          username: user.username,
          email: user.email,
        },
        expiresIn: accessTokenExpiry,
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        error: 'Login failed',
        message:
          NODE_ENV === 'development' ? error.message : 'Internal server error',
      });
    }
  }
);

// Refresh token endpoint
app.post('/api/auth/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({ error: 'Refresh token required' });
    }

    // Verify refresh token
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET);
    } catch (err) {
      return res.status(403).json({ error: 'Invalid refresh token' });
    }

    // Check if refresh token exists in storage
    const refreshTokens = await readRefreshTokens();
    const storedToken = Object.values(refreshTokens).find(
      t => t.token === refreshToken && t.userId === decoded.userId
    );

    if (!storedToken) {
      return res.status(403).json({ error: 'Refresh token not found' });
    }

    // Check if token is expired
    if (storedToken.expiresAt < Date.now()) {
      // Remove expired token
      const tokenId = Object.keys(refreshTokens).find(
        id => refreshTokens[id] === storedToken
      );
      delete refreshTokens[tokenId];
      await writeRefreshTokens(refreshTokens);
      return res.status(403).json({ error: 'Refresh token expired' });
    }

    // Get user data
    const users = await readUsers();
    const user = users[decoded.userId];

    if (!user || !user.isActive) {
      return res.status(403).json({ error: 'User not found or inactive' });
    }

    // Generate new access token
    const accessToken = jwt.sign(
      { userId: user.userId, username: user.username, role: 'user' },
      JWT_SECRET,
      { expiresIn: storedToken.rememberMe ? '24h' : '15m' }
    );

    console.log(
      `Token refreshed for user: ${user.username} (ID: ${user.userId})`
    );

    res.json({
      accessToken,
      expiresIn: storedToken.rememberMe ? '24h' : '15m',
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      error: 'Token refresh failed',
      message:
        NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
});

// Logout endpoint
app.post('/api/auth/logout', async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (refreshToken) {
      // Remove refresh token from storage
      const refreshTokens = await readRefreshTokens();
      const tokenId = Object.keys(refreshTokens).find(
        id => refreshTokens[id].token === refreshToken
      );

      if (tokenId) {
        delete refreshTokens[tokenId];
        await writeRefreshTokens(refreshTokens);
        console.log('Refresh token removed on logout');
      }
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout failed',
      message:
        NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
});

// Logout from all devices
app.post('/api/auth/logout-all', verifyToken, async (req, res) => {
  try {
    const userId = req.userId;

    // Remove all refresh tokens for this user
    const refreshTokens = await readRefreshTokens();
    const filteredTokens = {};

    Object.keys(refreshTokens).forEach(tokenId => {
      if (refreshTokens[tokenId].userId !== userId) {
        filteredTokens[tokenId] = refreshTokens[tokenId];
      }
    });

    await writeRefreshTokens(filteredTokens);
    console.log(`All refresh tokens removed for user: ${userId}`);

    res.json({ message: 'Logged out from all devices successfully' });
  } catch (error) {
    console.error('Logout all error:', error);
    res.status(500).json({
      error: 'Logout failed',
      message:
        NODE_ENV === 'development' ? error.message : 'Internal server error',
    });
  }
});

// --- Journal Entry API Routes (Now Require Auth) ---

// Get all entries for the logged-in user
app.get('/api/entries', verifyToken, async (req, res) => {
  try {
    const allEntriesData = await readAllEntriesData();
    const userEntries = allEntriesData[req.userId] || []; // Get entries for this user, default to empty array
    res.json(userEntries);
  } catch (error) {
    console.error(`Error fetching entries for user ${req.userId}:`, error);
    res.status(500).json({ error: 'Failed to fetch entries' });
  }
});

// Save new entry for the logged-in user
app.post('/api/entries', verifyToken, async (req, res) => {
  try {
    const userId = req.userId;
    const allEntriesData = await readAllEntriesData();
    const userEntries = allEntriesData[userId] || [];

    const newEntry = {
      id: uuidv4(), // Generate new ID using uuidv4
      text: req.body.text,
      timestamp: req.body.timestamp || new Date().toISOString(),
    };

    userEntries.push(newEntry);
    allEntriesData[userId] = userEntries; // Update the main data structure

    await writeAllEntriesData(allEntriesData);
    console.log(`Entry ${newEntry.id} saved for user ${userId}`);
    res.status(201).json(newEntry);
  } catch (error) {
    console.error(`Error saving entry for user ${req.userId}:`, error);
    res.status(500).json({ error: error.message || 'Failed to save entry' });
  }
});

// Update existing entry for the logged-in user
app.put('/api/entries/:id', verifyToken, async (req, res) => {
  try {
    const userId = req.userId;
    const entryId = req.params.id;
    const allEntriesData = await readAllEntriesData();
    const userEntries = allEntriesData[userId] || [];

    const entryIndex = userEntries.findIndex(entry => entry.id === entryId);

    if (entryIndex === -1) {
      console.log(
        `Update failed: Entry ${entryId} not found for user ${userId}`
      );
      return res.status(404).json({ error: 'Entry not found' });
    }

    // Update the existing entry
    const updatedEntry = {
      ...userEntries[entryIndex],
      text: req.body.text,
      timestamp: req.body.timestamp || new Date().toISOString(),
    };

    userEntries[entryIndex] = updatedEntry;
    allEntriesData[userId] = userEntries; // Update the main data structure

    await writeAllEntriesData(allEntriesData);
    console.log(`Entry ${entryId} updated for user ${userId}`);
    res.json(updatedEntry);
  } catch (error) {
    console.error(
      `Error updating entry ${req.params.id} for user ${req.userId}:`,
      error
    );
    res.status(500).json({ error: error.message || 'Failed to update entry' });
  }
});

// Delete entry for the logged-in user
app.delete('/api/entries/:id', verifyToken, async (req, res) => {
  try {
    const userId = req.userId;
    const entryId = req.params.id;
    const allEntriesData = await readAllEntriesData();
    const userEntries = allEntriesData[userId] || [];
    const initialCount = userEntries.length;

    const filteredEntries = userEntries.filter(entry => entry.id !== entryId);

    if (filteredEntries.length === initialCount) {
      console.log(
        `Delete failed: Entry ${entryId} not found for user ${userId}`
      );
      return res.status(404).json({ error: 'Entry not found' });
    }

    allEntriesData[userId] = filteredEntries; // Update the main data structure

    await writeAllEntriesData(allEntriesData);
    console.log(`Entry ${entryId} deleted for user ${userId}`);
    res.json({ message: 'Entry deleted successfully' });
  } catch (error) {
    console.error(
      `Error deleting entry ${req.params.id} for user ${req.userId}:`,
      error
    );
    res.status(500).json({ error: error.message || 'Failed to delete entry' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', environment: NODE_ENV });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Something went wrong' });
});

// Cleanup expired tokens periodically (every hour)
setInterval(cleanupExpiredTokens, 60 * 60 * 1000);

// Start the server
app.listen(PORT, () => {
  console.log(`Server running in ${NODE_ENV} mode on port ${PORT}`);
  console.log(`Users data file: ${USERS_FILE}`);
  console.log(`Entries data file: ${DATA_FILE}`);
  console.log(`Refresh tokens file: ${REFRESH_TOKENS_FILE}`);

  // Initial cleanup of expired tokens
  cleanupExpiredTokens();

  console.log('Security features enabled:');
  console.log('- Rate limiting');
  console.log('- Input validation and sanitization');
  console.log('- JWT refresh tokens');
  console.log('- Account lockout protection');
  console.log('- Enhanced password requirements');
});
