{"name": "voice-journal", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "terser": "^5.43.1", "vite": "^6.3.4"}}